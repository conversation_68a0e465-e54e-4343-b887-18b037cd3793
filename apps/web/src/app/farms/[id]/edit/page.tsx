'use client'

import { useState, useEffect, useCallback } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Image from 'next/image'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { ImageUpload } from '@/components/ui/FileUpload'
import { farmService } from '@/lib/firebase/firestore'
import { farmImageService } from '@/lib/firebase/storage'
import { Timestamp } from 'firebase/firestore'
import { useAuth } from '@/hooks/useAuth'
import { GameFarm, ActivityType, FarmImage } from '@/lib/types/firestore'
import { SOUTH_AFRICAN_PROVINCES, SouthAfricanProvince } from '@/lib/constants'
import { ArrowLeft, Save, Eye } from 'lucide-react'
import Link from 'next/link'

interface FarmFormData {
  name: string
  description: string
  descriptionAfrikaans: string
  location: string
  province: SouthAfricanProvince | ''
  sizeHectares: string
  activityTypes: ActivityType
  contactEmail: string
  contactPhone: string
  websiteUrl: string
  rules: string
  rulesAfrikaans: string
  pricingInfo: string
  isActive: boolean
  featured: boolean
}



export default function EditFarmPage() {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const farmId = params.id as string

  const [farm, setFarm] = useState<GameFarm | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [farmImages, setFarmImages] = useState<FarmImage[]>([])
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)

  const [formData, setFormData] = useState<FarmFormData>({
    name: '',
    description: '',
    descriptionAfrikaans: '',
    location: '',
    province: '',
    sizeHectares: '',
    activityTypes: 'both',
    contactEmail: '',
    contactPhone: '',
    websiteUrl: '',
    rules: '',
    rulesAfrikaans: '',
    pricingInfo: '',
    isActive: true,
    featured: false
  })

  const fetchFarm = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const farmData = await farmService.get(farmId)

      if (!farmData) {
        throw new Error('Farm not found')
      }

      // Ensure user owns this farm
      if (farmData.ownerId !== user!.uid) {
        throw new Error('Access denied - you do not own this farm')
      }

      setFarm(farmData)
      setFormData({
        name: farmData.name,
        description: farmData.description || '',
        descriptionAfrikaans: farmData.descriptionAfrikaans || '',
        location: farmData.location,
        province: farmData.province,
        sizeHectares: farmData.sizeHectares?.toString() || '',
        activityTypes: farmData.activityTypes,
        contactEmail: farmData.contactEmail,
        contactPhone: farmData.contactPhone || '',
        websiteUrl: farmData.websiteUrl || '',
        rules: farmData.rules || '',
        rulesAfrikaans: farmData.rulesAfrikaans || '',
        pricingInfo: farmData.pricingInfo || '',
        isActive: farmData.isActive,
        featured: farmData.featured
      })

      // Load farm images
      try {
        const images = await farmService.getImages(farmId)
        setFarmImages(images)
      } catch (imageError) {
        console.error('Error loading farm images:', imageError)
        // Don't fail the whole page if images can't be loaded
      }
    } catch (err) {
      console.error('Error fetching farm:', err)
      setError('Failed to load farm details')
    } finally {
      setLoading(false)
    }
  }, [farmId, user])

  useEffect(() => {
    if (!authLoading && user) {
      fetchFarm()
    }
  }, [authLoading, user, fetchFarm])

  const handleInputChange = (field: keyof FarmFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setSuccessMessage(null) // Clear success message when user makes changes
  }

  const handleImageUpload = async (imageUrl: string) => {
    try {
      // Add image to Firestore
      const newDisplayOrder = farmImages.length
      const isPrimary = farmImages.length === 0 // First image is primary

      const imageId = await farmService.addImage(farmId, {
        farmId,
        imageUrl,
        displayOrder: newDisplayOrder,
        isPrimary,
        altText: `Farm image ${newDisplayOrder + 1}`
      })

      // Update local state
      const newImage: FarmImage = {
        id: imageId,
        farmId,
        imageUrl,
        displayOrder: newDisplayOrder,
        isPrimary,
        altText: `Farm image ${newDisplayOrder + 1}`,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }

      setFarmImages(prev => [...prev, newImage])
      setSuccessMessage('Image uploaded successfully!')
    } catch (error) {
      console.error('Error uploading image:', error)
      setError('Failed to upload image')
    }
  }

  const handleImageRemove = async (imageId: string, imageUrl: string) => {
    try {
      // Delete from Firestore
      await farmService.deleteImage(farmId, imageId)

      // Delete from Storage
      await farmImageService.deleteFarmImage(farmId, imageUrl)

      // Update local state and reorder
      const filtered = farmImages.filter(img => img.id !== imageId)
      const reordered = filtered.map((img, index) => ({
        ...img,
        displayOrder: index,
        isPrimary: index === 0 && filtered.length > 0 // First image becomes primary
      }))

      // Update display orders in Firestore
      for (const img of reordered) {
        if (img.displayOrder !== farmImages.find(original => original.id === img.id)?.displayOrder ||
            img.isPrimary !== farmImages.find(original => original.id === img.id)?.isPrimary) {
          await farmService.updateImage(farmId, img.id!, {
            displayOrder: img.displayOrder,
            isPrimary: img.isPrimary
          })
        }
      }

      setFarmImages(reordered)
      setSuccessMessage('Image removed successfully!')
    } catch (error) {
      console.error('Error removing image:', error)
      setError('Failed to remove image')
    }
  }

  const handleSetPrimary = async (imageId: string) => {
    try {
      // Update all images to set only the selected one as primary
      const updatedImages = farmImages.map(img => ({
        ...img,
        isPrimary: img.id === imageId
      }))

      // Update in Firestore
      for (const img of updatedImages) {
        if (img.isPrimary !== farmImages.find(original => original.id === img.id)?.isPrimary) {
          await farmService.updateImage(farmId, img.id!, {
            isPrimary: img.isPrimary
          })
        }
      }

      setFarmImages(updatedImages)
      setSuccessMessage('Primary image updated!')
    } catch (error) {
      console.error('Error setting primary image:', error)
      setError('Failed to update primary image')
    }
  }

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/html', index.toString())
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = async (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null)
      return
    }

    try {
      const result = Array.from(farmImages)
      const [removed] = result.splice(draggedIndex, 1)
      result.splice(dropIndex, 0, removed)

      // Update display orders
      const reordered = result.map((img, index) => ({
        ...img,
        displayOrder: index
      }))

      // Update in Firestore
      for (const img of reordered) {
        if (img.displayOrder !== farmImages.find(original => original.id === img.id)?.displayOrder) {
          await farmService.updateImage(farmId, img.id!, {
            displayOrder: img.displayOrder
          })
        }
      }

      setFarmImages(reordered)
      setSuccessMessage('Images reordered successfully!')
    } catch (error) {
      console.error('Error reordering images:', error)
      setError('Failed to reorder images')
    }

    setDraggedIndex(null)
  }

  const handleDragEnd = () => {
    setDraggedIndex(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user || !farm) return

    try {
      setSaving(true)
      setError(null)

      const updateData = {
        name: formData.name,
        description: formData.description || undefined,
        descriptionAfrikaans: formData.descriptionAfrikaans || undefined,
        location: formData.location,
        province: formData.province as SouthAfricanProvince,
        sizeHectares: formData.sizeHectares ? parseInt(formData.sizeHectares) : undefined,
        activityTypes: formData.activityTypes,
        contactEmail: formData.contactEmail,
        contactPhone: formData.contactPhone || undefined,
        websiteUrl: formData.websiteUrl || undefined,
        rules: formData.rules || undefined,
        rulesAfrikaans: formData.rulesAfrikaans || undefined,
        pricingInfo: formData.pricingInfo || undefined,
        isActive: formData.isActive,
        featured: formData.featured
      }

      await farmService.update(farmId, updateData)

      setSuccessMessage('Farm details updated successfully!')

      // Refresh farm data
      await fetchFarm()
    } catch (err) {
      console.error('Error updating farm:', err)
      setError('Failed to update farm details')
    } finally {
      setSaving(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-earth-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"></div>
          <p className="mt-4 text-earth-600">Loading farm details...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    router.push('/auth/login')
    return null
  }

  if (error && !farm) {
    return (
      <div className="min-h-screen bg-earth-100 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-earth-900 mb-4">Farm Not Found</h1>
            <p className="text-earth-600 mb-6">{error}</p>
            <Link href="/dashboard">
              <Button variant="primary">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-earth-100 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <Link href={`/farms/${farmId}`}>
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-2" />
                View Public Page
              </Button>
            </Link>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-earth-900">Edit Farm Listing</h1>
              <p className="text-earth-600 mt-2">
                Update your farm details and settings
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={formData.isActive ? 'hunting' : 'default'}>
                {formData.isActive ? 'Active' : 'Inactive'}
              </Badge>
              {formData.featured && (
                <Badge variant="photo">Featured</Badge>
              )}
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {successMessage && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <p className="text-green-600 text-sm">{successMessage}</p>
            </div>
          )}

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-earth-700 mb-2">
                  Farm Name *
                </label>
                <Input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter your farm name"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-earth-700 mb-2">
                    Location/City *
                  </label>
                  <Input
                    id="location"
                    type="text"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="e.g., Lephalale"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="province" className="block text-sm font-medium text-earth-700 mb-2">
                    Province *
                  </label>
                  <select
                    id="province"
                    value={formData.province}
                    onChange={(e) => handleInputChange('province', e.target.value)}
                    className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                    required
                  >
                    <option value="">Select Province</option>
                    {SOUTH_AFRICAN_PROVINCES.map(province => (
                      <option key={province} value={province}>{province}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="sizeHectares" className="block text-sm font-medium text-earth-700 mb-2">
                  Farm Size (Hectares)
                </label>
                <Input
                  id="sizeHectares"
                  type="number"
                  value={formData.sizeHectares}
                  onChange={(e) => handleInputChange('sizeHectares', e.target.value)}
                  placeholder="e.g., 5000"
                />
              </div>

              <div>
                <label htmlFor="activityTypes" className="block text-sm font-medium text-earth-700 mb-2">
                  Activity Types *
                </label>
                <select
                  id="activityTypes"
                  value={formData.activityTypes}
                  onChange={(e) => handleInputChange('activityTypes', e.target.value as ActivityType)}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                  required
                >
                  <option value="hunting">Hunting Only</option>
                  <option value="photo_safari">Photo Safari Only</option>
                  <option value="both">Both Hunting & Photo Safari</option>
                </select>
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-earth-700 mb-2">
                  Description
                </label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe your farm, facilities, and what makes it special..."
                  rows={4}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="pricingInfo" className="block text-sm font-medium text-earth-700 mb-2">
                  Pricing Information
                </label>
                <Input
                  id="pricingInfo"
                  type="text"
                  value={formData.pricingInfo}
                  onChange={(e) => handleInputChange('pricingInfo', e.target.value)}
                  placeholder="e.g., From R2,500 per day"
                />
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="contactEmail" className="block text-sm font-medium text-earth-700 mb-2">
                    Contact Email *
                  </label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={formData.contactEmail}
                    onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="contactPhone" className="block text-sm font-medium text-earth-700 mb-2">
                    Contact Phone
                  </label>
                  <Input
                    id="contactPhone"
                    type="tel"
                    value={formData.contactPhone}
                    onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                    placeholder="+27 12 345 6789"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="websiteUrl" className="block text-sm font-medium text-earth-700 mb-2">
                  Website URL
                </label>
                <Input
                  id="websiteUrl"
                  type="text"
                  value={formData.websiteUrl}
                  onChange={(e) => handleInputChange('websiteUrl', e.target.value)}
                  placeholder="www.yourfarm.com"
                />
              </div>
            </CardContent>
          </Card>

          {/* Farm Images */}
          <Card>
            <CardHeader>
              <CardTitle>Farm Images</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-earth-700 mb-4">
                  Manage Farm Photos
                </label>
                <p className="text-sm text-earth-600 mb-4">
                  Add, remove, and reorder photos of your farm. Drag images to reorder them.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Existing Images */}
                  {farmImages.map((image, index) => (
                    <div
                      key={image.id}
                      className={`relative group cursor-move transition-all duration-200 ${
                        draggedIndex === index ? 'opacity-50 scale-95' : 'opacity-100 scale-100'
                      }`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, index)}
                      onDragOver={handleDragOver}
                      onDrop={(e) => handleDrop(e, index)}
                      onDragEnd={handleDragEnd}
                    >
                      <div className="relative w-full h-48 rounded-lg border overflow-hidden">
                        <Image
                          src={image.imageUrl}
                          alt={image.altText || `Farm image ${index + 1}`}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      </div>

                      {/* Drag handle indicator */}
                      <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                        ⋮⋮ Drag to reorder
                      </div>

                      <div className="absolute top-2 right-2 flex gap-2">
                        {image.isPrimary && (
                          <span className="bg-accent-600 text-white text-xs px-2 py-1 rounded">
                            Main Photo
                          </span>
                        )}
                        {!image.isPrimary && (
                          <button
                            type="button"
                            onClick={() => handleSetPrimary(image.id!)}
                            className="bg-earth-600 hover:bg-accent-600 text-white text-xs px-2 py-1 rounded transition-colors"
                          >
                            Set as Main
                          </button>
                        )}
                      </div>

                      <button
                        type="button"
                        onClick={() => handleImageRemove(image.id!, image.imageUrl)}
                        className="absolute bottom-2 right-2 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        ×
                      </button>

                      {/* Display order indicator */}
                      <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                        {index + 1}
                      </div>
                    </div>
                  ))}

                  {/* Upload New Image */}
                  {farmImages.length < 10 && user && (
                    <ImageUpload
                      bucket="farm-images"
                      farmId={farmId}
                      onUpload={handleImageUpload}
                      maxSize={10}
                      className="h-48"
                    />
                  )}
                </div>

                {farmImages.length >= 10 && (
                  <p className="text-sm text-earth-500 mt-2">
                    Maximum of 10 images allowed. Remove an image to add more.
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Farm Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Farm Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-earth-700">Active Status</h4>
                  <p className="text-sm text-earth-600">Control whether your farm is visible to customers</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => handleInputChange('isActive', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-accent-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-earth-700">Featured Listing</h4>
                  <p className="text-sm text-earth-600">Featured farms appear at the top of search results</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.featured}
                    onChange={(e) => handleInputChange('featured', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-accent-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent-600"></div>
                </label>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end gap-4">
            <Link href="/dashboard">
              <Button variant="outline" type="button">
                Cancel
              </Button>
            </Link>
            <Button
              type="submit"
              variant="primary"
              disabled={saving}
              className="min-w-[120px]"
            >
              {saving ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </div>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
