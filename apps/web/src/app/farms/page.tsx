'use client'

import { useState, useEffect, useCallback, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { SearchBar } from '@/components/features/SearchBar'
import { FarmCard } from '@/components/features/FarmCard'
import { FilterPanel, FilterOptions } from '@/components/features/FilterPanel'
import { HeroImageLoader } from '@/components/ui/HeroImageLoader'
import { farmService } from '@/lib/firebase/firestore'
import { GameFarm } from '@/lib/types/firestore'

interface FarmWithStats extends GameFarm {
  rating?: number
  reviewCount?: number
  activities: ('hunting' | 'photo_safari')[]
  primaryImageUrl?: string
}

function FarmsPageContent() {
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState('')
  const [locationQuery, setLocationQuery] = useState('')
  const [filters, setFilters] = useState<FilterOptions>({
    activities: [],
    provinces: [],
    priceRange: [0, 20000],
    amenities: [],
    species: []
  })
  const [farms, setFarms] = useState<FarmWithStats[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Handle URL parameters for activity filtering
  useEffect(() => {
    const activity = searchParams.get('activity')
    if (activity === 'hunting' || activity === 'photo_safari') {
      setFilters(prev => ({
        ...prev,
        activities: [activity]
      }))
    }
  }, [searchParams])

  const fetchFarms = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const farmsData = await farmService.getAllWithPrimaryImages({
        isActive: true
      })

      // Process farms data and calculate stats
      const processedFarms: FarmWithStats[] = farmsData.map(farm => {
        // Convert activityTypes to activities array
        const activities: ('hunting' | 'photo_safari')[] = []
        if (farm.activityTypes === 'hunting') activities.push('hunting')
        else if (farm.activityTypes === 'photo_safari') activities.push('photo_safari')
        else if (farm.activityTypes === 'both') {
          activities.push('hunting', 'photo_safari')
        }

        return {
          ...farm,
          activities,
          rating: undefined, // TODO: Calculate from reviews subcollection
          reviewCount: 0, // TODO: Count from reviews subcollection
          primaryImageUrl: farm.primaryImageUrl
        }
      })

      setFarms(processedFarms)
    } catch (err) {
      console.error('Error fetching farms:', err)
      setError('Failed to load farms')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchFarms()
  }, [fetchFarms])

  const handleSearch = (query: string, location: string) => {
    setSearchQuery(query)
    setLocationQuery(location)
  }

  const filteredFarms = farms.filter(farm => {
    const matchesQuery = !searchQuery ||
      farm.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (farm.description && farm.description.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesLocation = !locationQuery ||
      farm.location.toLowerCase().includes(locationQuery.toLowerCase()) ||
      farm.province.toLowerCase().includes(locationQuery.toLowerCase())

    const matchesActivity = filters.activities.length === 0 ||
      filters.activities.some(activity => farm.activities.includes(activity))

    const matchesProvince = filters.provinces.length === 0 ||
      filters.provinces.includes(farm.province)

    return matchesQuery && matchesLocation && matchesActivity && matchesProvince
  })

  return (
    <div className="min-h-screen bg-earth-100">
      {/* Hero Section with Search */}
      <div className="relative text-white h-96">
        <HeroImageLoader className="absolute inset-0">
          <div className="relative z-20 container mx-auto px-4 h-full flex items-center">
            <div className="text-center mb-8">
            <h1 className="text-4xl text-earth-200 md:text-5xl font-bold mb-4 font-display">
              Find Your Perfect Safari Experience
            </h1>
            <p className="text-xl text-earth-200 max-w-2xl mx-auto">
              Discover exceptional hunting and photo safari opportunities across South Africa
            </p>
            </div>
            <div className="max-w-4xl mx-auto">
              <SearchBar onSearch={handleSearch} />
            </div>
          </div>
        </HeroImageLoader>
      </div>

      {/* Results Section */}
      <div className="container mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar with Filters */}
          <div className="lg:w-1/4">
            <FilterPanel
              filters={filters}
              onFiltersChange={setFilters}
              className="sticky top-24"
            />
          </div>

          {/* Main Content */}
          <div className="lg:w-3/4">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold text-earth-900">
                {loading ? 'Loading...' : `${filteredFarms.length} Farms Found`}
              </h2>

              {(searchQuery || locationQuery) && (
                <div className="text-earth-600">
                  {searchQuery && <span>Search: &ldquo;{searchQuery}&rdquo; </span>}
                  {locationQuery && <span>Location: &ldquo;{locationQuery}&rdquo;</span>}
                </div>
              )}
            </div>

            {loading ? (
              <div className="text-center py-16">
                <p className="text-earth-600 text-lg">Loading farms...</p>
              </div>
            ) : error ? (
              <div className="text-center py-16">
                <p className="text-red-600 text-lg">{error}</p>
                <button
                  onClick={fetchFarms}
                  className="mt-4 px-4 py-2 bg-accent-600 text-white rounded-lg hover:bg-accent-700"
                >
                  Try Again
                </button>
              </div>
            ) : filteredFarms.length === 0 ? (
              <div className="text-center py-16">
                <p className="text-earth-600 text-lg">
                  No farms found matching your criteria. Try adjusting your search or filters.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredFarms.map((farm) => (
                  <FarmCard
                    key={farm.id}
                    id={farm.id}
                    name={farm.name}
                    location={farm.location}
                    province={farm.province}
                    description={farm.description || ''}
                    imageUrl={farm.primaryImageUrl}
                    activities={farm.activities}
                    priceRange={farm.pricingInfo || 'Contact for pricing'}
                    rating={farm.rating}
                    reviewCount={farm.reviewCount}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default function FarmsPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-earth-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"></div>
        <p className="mt-4 text-earth-600">Loading farms...</p>
      </div>
    </div>}>
      <FarmsPageContent />
    </Suspense>
  )
}