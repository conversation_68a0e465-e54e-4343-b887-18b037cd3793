/**
 * Centralized constants for the BvR Safaris application
 * This file contains all shared constants to ensure consistency across the application
 */

/**
 * South African provinces in alphabetical order
 * Used throughout the application for province selection, filtering, and display
 */
export const SOUTH_AFRICAN_PROVINCES = [
  'Eastern Cape',
  'Free State',
  'Gauteng',
  'KwaZulu-Natal',
  'Limpopo',
  'Mpumalanga',
  'Northern Cape',
  'North West',
  'Western Cape'
] as const

/**
 * Type for South African provinces to ensure type safety
 */
export type SouthAfricanProvince = typeof SOUTH_AFRICAN_PROVINCES[number]

/**
 * Activity types available on the platform
 */
export const ACTIVITY_TYPES = {
  HUNTING: 'hunting',
  PHOTO_SAFARI: 'photo_safari',
  BOTH: 'both'
} as const

/**
 * Booking status options
 */
export const BOOKING_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed'
} as const

/**
 * User roles available in the system
 * Updated to use the simplified role system with admin support
 */
export const USER_ROLES = {
  FARM_OWNER: 'farm_owner',
  GUEST: 'guest',
  ADMIN: 'admin'
} as const

/**
 * Default price range for filtering (in ZAR)
 */
export const DEFAULT_PRICE_RANGE = [0, 20000] as const

/**
 * Maximum file size for uploads (in bytes)
 */
export const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB

/**
 * Supported image formats for uploads
 */
export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp'
] as const

/**
 * Default pagination limits
 */
export const PAGINATION = {
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  FEATURED_FARMS_LIMIT: 3
} as const

/**
 * Farm amenities available for filtering
 * Used in filter panels and farm feature listings
 */
export const FARM_AMENITIES = [
  'Luxury Lodge',
  'Basic Accommodation',
  'Restaurant',
  'Bar',
  'Swimming Pool',
  'Spa',
  'WiFi',
  'Airport Transfer',
  'Professional Guide',
  'Trophy Preparation',
  'Taxidermy'
] as const

/**
 * Type for farm amenities to ensure type safety
 */
export type FarmAmenity = typeof FARM_AMENITIES[number]

/**
 * Game species available for hunting and viewing
 * Used in filter panels and farm species listings
 */
export const GAME_SPECIES = [
  'Lion',
  'Leopard',
  'Elephant',
  'Buffalo',
  'Rhino',
  'Kudu',
  'Impala',
  'Springbok',
  'Eland',
  'Sable',
  'Gemsbok',
  'Waterbuck',
  'Bushbuck',
  'Warthog'
] as const

/**
 * Type for game species to ensure type safety
 */
export type GameSpeciesName = typeof GAME_SPECIES[number]
