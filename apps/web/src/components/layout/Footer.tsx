import Link from 'next/link'

export function Footer() {
  return (
    <footer className="bg-[var(--dark-gray)] text-white mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center mb-4">
              <span className="text-2xl mr-2">Proudly South African</span>
              <h3 className="text-xl font-bold font-[var(--font-display)]">BvR Safaris</h3>
            </div>
            <p className="text-gray-300 mb-4">
              South Africa&apos;s premier platform for booking authentic hunting and photo safari experiences. 
              Connect with certified game farms and create unforgettable memories in the African wilderness.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                <span className="sr-only">Facebook</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="#" className="text-gray-300 hover:text-white transition-colors">
                <span className="sr-only">Instagram</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.32-1.297C4.198 14.926 3.652 13.18 3.652 11.987c0-1.297.49-2.448 1.297-3.32.865-.865 2.023-1.297 3.32-1.297 1.297 0 2.448.49 3.32 1.297.865.865 1.297 2.023 1.297 3.32 0 1.297-.49 2.448-1.297 3.32-.865.865-2.023 1.297-3.32 1.297zm7.718-9.996c-.33 0-.612-.282-.612-.612s.282-.612.612-.612.612.282.612.612-.282.612-.612.612zm-3.573 2.248c-1.297 0-2.344 1.047-2.344 2.344s1.047 2.344 2.344 2.344 2.344-1.047 2.344-2.344-1.047-2.344-2.344-2.344z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/farms" className="text-gray-300 hover:text-white transition-colors">
                  Browse Farms
                </Link>
              </li>
              <li>
                <Link href="/farms?activity=hunting" className="text-gray-300 hover:text-white transition-colors">
                  Hunting Safaris
                </Link>
              </li>
              <li>
                <Link href="/farms?activity=photo_safari" className="text-gray-300 hover:text-white transition-colors">
                  Photo Safaris
                </Link>
              </li>
              <li>
                <Link href="/how-it-works" className="text-gray-300 hover:text-white transition-colors">
                  How It Works
                </Link>
              </li>
              <li>
                <Link href="/safety" className="text-gray-300 hover:text-white transition-colors">
                  Safety Guidelines
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="font-semibold mb-4">Support</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-white transition-colors">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-gray-300 hover:text-white transition-colors">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/help" className="text-gray-300 hover:text-white transition-colors">
                  Help Center
                </Link>
              </li>
              <li>
                <Link href="/farm-owners" className="text-gray-300 hover:text-white transition-colors">
                  For Farm Owners
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-300 hover:text-white transition-colors">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-gray-300 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-300 text-sm">
            © {new Date().getFullYear()} BvR Safaris. All rights reserved.
          </p>
          <div className="flex items-center space-x-4 mt-4 md:mt-0">
            <span className="text-gray-300 text-sm">Proudly South African</span>
          </div>
        </div>
      </div>
    </footer>
  )
}